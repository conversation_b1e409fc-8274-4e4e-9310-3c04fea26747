<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Domains;

use App\Domains\WhatsApp\ChangeValue;
use App\Domains\WhatsApp\ChangeValueMessage;

class MessageData
{
    public string $message_id;
    public string $from;
    public string $original_from;
    public string $type;
    public ?string $content;
    public int $timestamp;
    public ?string $contact_name;
    public ?string $profile_name;
    public ?string $phone_number_id;
    public ?string $display_phone_number;
    public array $raw_message;
    public array $raw_metadata;
    public array $raw_contacts;

    // New fields for button detection
    public bool $is_button_click;
    public bool $is_raw_message;
    public ?string $button_payload;
    public ?string $button_click_id;
    public ?array $interactive_selection;
    public ?string $button_text;
    public ?string $raw_data;

    public function __construct(
        string $message_id,
        string $from,
        string $original_from,
        string $type,
        ?string $content,
        int $timestamp,
        ?string $contact_name = null,
        ?string $profile_name = null,
        ?string $phone_number_id = null,
        ?string $display_phone_number = null,
        array $raw_message = [],
        array $raw_metadata = [],
        array $raw_contacts = [],
        bool $is_button_click = false,
        bool $is_raw_message = true,
        ?string $button_payload = null,
        ?string $button_click_id = null,
        ?array $interactive_selection = null,
        ?string $button_text = null,
        ?string $raw_data = null
    ) {
        $this->message_id = $message_id;
        $this->from = $from;
        $this->original_from = $original_from;
        $this->type = $type;
        $this->content = $content;
        $this->timestamp = $timestamp;
        $this->contact_name = $contact_name;
        $this->profile_name = $profile_name;
        $this->phone_number_id = $phone_number_id;
        $this->display_phone_number = $display_phone_number;
        $this->raw_message = $raw_message;
        $this->raw_metadata = $raw_metadata;
        $this->raw_contacts = $raw_contacts;
        $this->is_button_click = $is_button_click;
        $this->is_raw_message = $is_raw_message;
        $this->button_payload = $button_payload;
        $this->button_click_id = $button_click_id;
        $this->interactive_selection = $interactive_selection;
        $this->button_text = $button_text;
        $this->raw_data = $raw_data;
    }

    /**
     * Convert to array for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'message_id' => $this->message_id,
            'from' => $this->from,
            'original_from' => $this->original_from,
            'type' => $this->type,
            'content' => $this->content,
            'timestamp' => $this->timestamp,
            'contact_name' => $this->contact_name,
            'profile_name' => $this->profile_name,
            'phone_number_id' => $this->phone_number_id,
            'display_phone_number' => $this->display_phone_number,
            'raw_message' => $this->raw_message,
            'raw_metadata' => $this->raw_metadata,
            'raw_contacts' => $this->raw_contacts,
            'is_button_click' => $this->is_button_click,
            'is_raw_message' => $this->is_raw_message,
            'button_payload' => $this->button_payload,
            'button_click_id' => $this->button_click_id,
            'interactive_selection' => $this->interactive_selection,
            'button_text' => $this->button_text,
            'raw_data' => $this->raw_data,
        ];
    }

    /**
     * Check if this is a button click interaction
     */
    public function isButtonClick(): bool
    {
        return $this->is_button_click;
    }

    /**
     * Check if this is a raw text message
     */
    public function isRawMessage(): bool
    {
        return $this->is_raw_message;
    }

    /**
     * Get button click data for interaction storage
     */
    public function getButtonClickData(): array
    {
        return [
            'button_payload' => $this->button_payload,
            'interactive_selection' => $this->interactive_selection,
            'button_text' => $this->button_text,
            'timestamp' => $this->timestamp,
            'message_type' => $this->type
        ];
    }

    /**
     * Get raw message data for interaction storage
     */
    public function getRawMessageData(): array
    {
        return [
            'content' => $this->content,
            'message_type' => $this->type,
            'timestamp' => $this->timestamp,
            'contact_name' => $this->contact_name,
            'profile_name' => $this->profile_name
        ];
    }

    /**
     * Get interaction raw data as JSON string
     */
    public function getInteractionRawData(): string
    {
        if ($this->is_button_click) {
            return json_encode($this->getButtonClickData());
        } else {
            return json_encode($this->getRawMessageData());
        }
    }

    /**
     * Detect if message contains button click based on ChangeValueMessage
     */
    public static function detectButtonClick(ChangeValueMessage $changeValueMessage): bool
    {
        return $changeValueMessage->isInteractive();
    }

    /**
     * Extract button payload from ChangeValueMessage
     */
    public static function extractButtonPayload(ChangeValueMessage $changeValueMessage): ?string
    {
        if ($changeValueMessage->type === 'button') {
            return $changeValueMessage->getButtonPayload();
        }

        if ($changeValueMessage->type === 'interactive') {
            $selection = $changeValueMessage->getInteractiveSelection();
            return $selection['id'] ?? null;
        }

        return null;
    }

    /**
     * Extract button text from ChangeValueMessage
     */
    public static function extractButtonText(ChangeValueMessage $changeValueMessage): ?string
    {
        if ($changeValueMessage->type === 'button') {
            return $changeValueMessage->button['text'] ?? null;
        }

        if ($changeValueMessage->type === 'interactive') {
            $selection = $changeValueMessage->getInteractiveSelection();
            return $selection['title'] ?? null;
        }

        return null;
    }

    /**
     * Extract interactive selection from ChangeValueMessage
     */
    public static function extractInteractiveSelection(ChangeValueMessage $changeValueMessage): ?array
    {
        return $changeValueMessage->getInteractiveSelection();
    }
}
