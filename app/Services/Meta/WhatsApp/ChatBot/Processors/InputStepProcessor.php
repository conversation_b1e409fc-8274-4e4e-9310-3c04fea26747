<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\StepProcessorResult;
use App\Services\Meta\WhatsApp\ChatBot\Factories\StepProcessorResultFactory;
use App\Enums\ChatBot\StepType;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\VariableSubstitution\VariableSubstitutionService;

/**
 * Processor for INPUT type steps
 *
 * Handles input steps that collect user data, validate it,
 * and update domain objects accordingly. Integrates with
 * DynamicInputService for domain object updates.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class InputStepProcessor implements StepProcessorInterface
{
    protected DynamicInputService $dynamicInputService;
    protected VariableSubstitutionService $variableSubstitutionService;
    protected StepProcessorResultFactory $resultFactory;

    public function __construct(
        DynamicInputService $dynamicInputService,
        VariableSubstitutionService $variableSubstitutionService,
        StepProcessorResultFactory $resultFactory
    ) {
        $this->dynamicInputService = $dynamicInputService;
        $this->variableSubstitutionService = $variableSubstitutionService;
        $this->resultFactory = $resultFactory;
    }

    /**
     * Process an input step
     *
     * Input steps either request input from the user or process
     * the input they provided, validating and storing it.
     *
     * @param Step $step
     * @param Interaction $interaction
     * @param Conversation $conversation
     * @return StepProcessorResult
     * @throws \Exception
     */
    public function process(
        Step $step,
        Interaction $interaction,
        Conversation $conversation
    ): StepProcessorResult {
        $userInput = $interaction->getTextContent();

        if ($userInput) {
            $inputResult = $this->dynamicInputService->processInputStep($step, $interaction, $conversation);

            if ($inputResult['success']) {
                return $this->resultFactory->createInputProcessed(
                    step: $step,
                    input: $userInput,
                    updated_field: $inputResult['updated_field'],
                    message: "✅ Got it! Your {$inputResult['updated_field']} has been saved."
                );
            } else {
                // Handle input processing failure
                if ($inputResult['finish_flow'] ?? false) {
                    return $this->resultFactory->createInputErrorFinish(
                        step: $step,
                        error: $inputResult['error']
                    );
                } else {
                    return $this->resultFactory->createInputInvalid(
                        step: $step,
                        error: $inputResult['error']
                    );
                }
            }
        } else {
            // Request input
            $message = $this->getStepMessage($step);
            $processedMessage = $this->processMessageVariables($message, $conversation);

            return $this->resultFactory->createInputRequest(
                step: $step,
                message: $processedMessage,
                original_message: $message
            );
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::INPUT;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::INPUT->value];
    }

    /**
     * Get message content for step
     *
     * @param Step $step
     * @return string
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData)) {
                // Check for prompt field (specific to input steps)
                if (isset($jsonData['prompt'])) {
                    return $jsonData['prompt'];
                }
                // Check for message field
                if (isset($jsonData['message'])) {
                    return $jsonData['message'];
                }
                // Check for text field
                if (isset($jsonData['text'])) {
                    return $jsonData['text'];
                }
            }
        }

        // Fallback to step name
        return $step->step ?? 'Please provide your input:';
    }

    /**
     * Process message variables using VariableSubstitutionService
     */
    protected function processMessageVariables(string $message, Conversation $conversation): string
    {
        $availableModels = $this->getAvailableModelsForSubstitution($conversation);
        return $this->variableSubstitutionService->substitute($message, $availableModels);
    }

    /**
     * Get available models for variable substitution
     */
    protected function getAvailableModelsForSubstitution(Conversation $conversation): array
    {
        $models = [];

        if ($conversation->client) {
            $models['client'] = $conversation->client;
        }

        if ($conversation->phone_number) {
            $models['phone_number'] = $conversation->phone_number;
        }

        // TODO: Add support for dynamic variables (order.*, etc) from conversation JSON
        // This will be implemented when we add conversation variable storage

        return $models;
    }
}
