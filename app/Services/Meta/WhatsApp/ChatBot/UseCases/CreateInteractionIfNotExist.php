<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Factories\ChatBot\InteractionFactory;
use App\Helpers\DBLog;
use App\Repositories\InteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\MessageData;
use Exception;

class CreateInteractionIfNotExist
{
    protected InteractionRepository $interactionRepository;
    protected InteractionFactory $interactionFactory;

    public function __construct(
        InteractionRepository $interactionRepository,
        InteractionFactory $interactionFactory
    )
    {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
    }

    /**
     * @throws Exception
     */
    public function perform(MessageData $messageData, Conversation $conversation): Interaction
    {
        $domain = $this->interactionFactory->buildFromWebhookData($messageData, $conversation);

        $interaction = $this->interactionRepository->findByWhatsAppMessageId($domain->whatsapp_message_id);
        if ($interaction) {
            DBLog::logInfo(
                "Interaction already exists, skipping creation and processing",
                "CreateInteractionIfNotExist::perform",
                $conversation->organization_id,
                null,
                [
                    'interaction_id' => $interaction->id,
                    'whatsapp_message_id' => $interaction->whatsapp_message_id
                ]
            );
            throw new Exception('Interaction already exists');
        }
        return $this->interactionRepository->save(
            $domain,
            $conversation->organization_id
        );
    }
}
