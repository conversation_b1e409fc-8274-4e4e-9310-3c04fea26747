<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Helpers\DBLog;
use App\Repositories\ConversationRepository;
use Exception;

class CloseManually
{
    protected ConversationRepository $conversationRepository;

    public function __construct(ConversationRepository $conversationRepository)
    {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Close a conversation manually by setting is_finished = true
     *
     * @param int $conversationId
     * @return Conversation
     * @throws Exception
     */
    public function perform(int $conversationId): Conversation
    {
        try {
            $organization_id = request()->user()->organization_id;
            $conversation = $this->conversationRepository->fetchById($conversationId);

            if (!$conversation) {
                throw new Exception("Conversation not found with ID: {$conversationId}");
            }
            if ($conversation->organization_id !== $organization_id) {
                throw new Exception("This conversation doesn't belong to this organization.");
            }

            if ($conversation->is_finished) {
                DBLog::logInfo(
                    "Conversation already finished",
                    "ChatBot::CloseManually::perform",
                    $conversation->organization_id,
                    auth()->id(),
                    [
                        'conversation_id' => $conversationId,
                        'status' => 'already_finished'
                    ]
                );

                return $conversation;
            }

            $conversation->finish();

            $this->conversationRepository->update($conversation, $conversation->organization_id);

            DBLog::logInfo(
                "Conversation closed manually",
                "ChatBot::CloseManually::perform",
                $conversation->organization_id,
                auth()->id(),
                [
                    'conversation_id' => $conversationId,
                    'client_id' => $conversation->client_id,
                    'flow_id' => $conversation->flow_id,
                    'status' => 'closed_manually'
                ]
            );

            return $conversation;

        } catch (Exception $e) {
            DBLog::logError(
                "Failed to close conversation manually",
                "ChatBot::CloseManually::perform",
                null,
                auth()->id(),
                [
                    'conversation_id' => $conversationId,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
