<?php

namespace App\Repositories;

use App\Domains\ChatBot\Interaction as InteractionDomain;
use App\Domains\Filters\InteractionFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\InteractionFactory;
use App\Models\Interaction;
use EloquentBuilder;

class InteractionRepository
{
    private InteractionFactory $interactionFactory;

    public function __construct(InteractionFactory $interactionFactory){
        $this->interactionFactory = $interactionFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(InteractionFilters $filters, OrderBy $orderBy) : array {
        $interactions = [];

        $models = EloquentBuilder::to(Interaction::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $interactions[] = $this->interactionFactory->buildFromModel($model);
        }

        return [
            'data' => $interactions,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, InteractionFilters $filters, OrderBy $orderBy) : array {
        $interactions = [];

        $models = EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $interactions[] = $this->interactionFactory->buildFromModel($model);
        }

        return [
            'data' => $interactions,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, InteractionFilters $filters): int {
        return EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, InteractionFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(InteractionDomain $interaction) : InteractionDomain {
        $savedInteraction = Interaction::create($interaction->toStoreArray());

        $interaction->id = $savedInteraction->id;

        return $interaction;
    }

    public function update(InteractionDomain $interaction, int $organization_id) : InteractionDomain {
        Interaction::where('id', $interaction->id)
            ->where('organization_id', $organization_id)
            ->update($interaction->toUpdateArray());

        return $interaction;
    }

    public function save(InteractionDomain $interaction, int $organization_id) : InteractionDomain {
        if ($interaction->id){
            return $this->update($interaction, $organization_id);
        }
        return $this->store($interaction);
    }

    public function fetchById(int $id) : InteractionDomain {
        return $this->interactionFactory->buildFromModel(
            Interaction::findOrFail($id)
        );
    }

    public function delete(InteractionDomain $interaction) : bool {
        return Interaction::find($interaction->id)->delete();
    }

    /**
     * Get the last interaction for a conversation
     *
     * @param int $conversationId
     * @return InteractionDomain|null
     */
    public function getLastByConversationId(int $conversationId): ?InteractionDomain
    {
        $interaction = Interaction::where('conversation_id', $conversationId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$interaction) {
            return null;
        }

        return $this->interactionFactory->buildFromModel($interaction);
    }

    /**
     * Get interactions for conversation
     */
    public function getInteractionsForConversation(int $conversationId): array
    {
        $interactions = Interaction::where('conversation_id', $conversationId)
            ->with(['step', 'flow'])
            ->orderBy('created_at', 'asc')
            ->get();

        return $interactions->map(function ($interaction) {
            return $this->interactionFactory->buildFromModel($interaction);
        })->filter()->toArray();
    }

    /**
     * Get latest interaction for conversation
     */
    public function getLatestInteraction(int $conversationId): ?InteractionDomain
    {
        $interaction = Interaction::where('conversation_id', $conversationId)
            ->with(['step', 'flow'])
            ->orderBy('created_at', 'desc')
            ->first();

        return $this->interactionFactory->buildFromModel($interaction);
    }

    /**
     * Find interaction by WhatsApp message ID
     */
    public function findByWhatsAppMessageId(string $messageId): ?InteractionDomain
    {
        $interaction = Interaction::whereJsonContains('json->whatsapp_message_id', $messageId)
            ->with(['conversation', 'step', 'flow'])
            ->first();

        return $this->interactionFactory->buildFromModel($interaction);
    }

    /**
     * Get last interaction for specific step and conversation
     */
    public function getLastForStep(int $stepId, int $conversationId): ?InteractionDomain
    {
        $interaction = Interaction::where('step_id', $stepId)
            ->where('conversation_id', $conversationId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $interaction ? $this->interactionFactory->buildFromModel($interaction) : null;
    }

    /**
     * Find pending interaction waiting for response
     */
    public function findPendingInteraction(int $conversationId): ?InteractionDomain
    {
        $interaction = Interaction::where('conversation_id', $conversationId)
            ->where('state', 'sent')
            ->where('requires_response', true)
            ->orderBy('created_at', 'desc')
            ->first();

        return $interaction ? $this->interactionFactory->buildFromModel($interaction) : null;
    }

    /**
     * Find interactions by state
     */
    public function findByState(string $state, int $limit = 100): array
    {
        $interactions = Interaction::where('state', $state)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $interactions->map(fn($interaction) => $this->interactionFactory->buildFromModel($interaction))->toArray();
    }

    /**
     * Find expired interactions
     */
    public function findExpiredInteractions(): array
    {
        $interactions = Interaction::where('state', 'sent')
            ->where('requires_response', true)
            ->whereNotNull('timeout_seconds')
            ->whereNotNull('sent_at')
            ->whereRaw('DATETIME(sent_at, "+" || timeout_seconds || " seconds") < DATETIME("now")')
            ->get();

        return $interactions->map(fn($interaction) => $this->interactionFactory->buildFromModel($interaction))->toArray();
    }
}
