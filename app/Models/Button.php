<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Button extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'buttons';

    protected $fillable = [
        'organization_id',
        'text',
        'type',
        'internal_type',
        'internal_data',
        'callback_data',
        'json',
        'commands_to_run',
        'step_to_go',
        'value',
    ];

    protected $casts = [
        'commands_to_run' => 'array',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }

    public function components() {
        return $this->belongsToMany(Component::class, 'buttons_components', 'button_id', 'component_id');
    }
}
