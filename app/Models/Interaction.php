<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Interaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'interactions';

    protected $fillable = [
        'organization_id',
        'user_id',
        'client_id',
        'flow_id',
        'step_id',
        'conversation_id',
        'message',
        'answer',
        'result',
        'json',
        'is_button_click',
        'button_click_id',
        'is_raw_message',
        'raw_data',
        'state',
        'sent_at',
        'responded_at',
        'processed_at',
        'requires_response',
        'expected_response_type',
        'timeout_seconds',
        'whatsapp_message_id',
        'whatsapp_message_type',
        'whatsapp_raw_data',
    ];

    protected $casts = [
        'is_button_click' => 'boolean',
        'is_raw_message' => 'boolean',
        'requires_response' => 'boolean',
        'sent_at' => 'datetime',
        'responded_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function flow()
    {
        return $this->belongsTo(Flow::class);
    }

    public function step()
    {
        return $this->belongsTo(Step::class);
    }

    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }

    public function buttonClick()
    {
        return $this->belongsTo(Button::class, 'button_click_id');
    }
}
