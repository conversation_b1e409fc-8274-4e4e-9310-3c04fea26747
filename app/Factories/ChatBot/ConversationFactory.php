<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Conversation;
use App\Factories\Inventory\ClientFactory;
use App\Factories\UserFactory;
use App\Http\Requests\Conversation\StoreRequest;
use App\Http\Requests\Conversation\UpdateRequest;
use App\Models\Conversation as ConversationModel;
use Illuminate\Support\Collection;

class ConversationFactory
{
    public UserFactory $userFactory;
    public ClientFactory $clientFactory;
    public FlowFactory $flowFactory;

    public function __construct(
        UserFactory $userFactory,
        ClientFactory $clientFactory,
        FlowFactory $flowFactory
    ) {
        $this->userFactory = $userFactory;
        $this->clientFactory = $clientFactory;
        $this->flowFactory = $flowFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): Conversation
    {
        return new Conversation(
            id: null,
            organization_id: $request->organization_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            flow_id: $request->flow_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            current_step_id: $request->current_step_id ?? null,
            json: $request->json ?? null,
            is_finished: $request->is_finished ?? false,
            raw_data: $request->raw_data ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): Conversation
    {
        return new Conversation(
            id: null,
            organization_id: $request->organization_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            flow_id: $request->flow_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            current_step_id: $request->current_step_id ?? null,
            json: $request->json ?? null,
            is_finished: $request->is_finished ?? null,
            raw_data: $request->raw_data ?? null
        );
    }

    public function buildFromModel(?ConversationModel $conversation, bool $with_relations = true): ?Conversation
    {
        if (!$conversation) {
            return null;
        }

        $interactions = null;
        if ($with_relations && $conversation->id) {
            $interactions = app()->make(InteractionFactory::class)->buildFromModels($conversation->interactions, false);
        }

        // Load phone_number if available and relationship is loaded (using app()->make() to avoid circular dependency)
        $phoneNumber = null;
        if ($with_relations && $conversation->phone_number_id && $conversation->relationLoaded('phoneNumber') && $conversation->phoneNumber) {
            $phoneNumberFactory = app()->make(PhoneNumberFactory::class);
            $phoneNumber = $phoneNumberFactory->buildFromModel($conversation->phoneNumber);
        }

        // Load current_step if available and relationship is loaded (using app()->make() to avoid circular dependency)
        $currentStep = null;
        if ($with_relations && $conversation->current_step_id && $conversation->relationLoaded('currentStep') && $conversation->currentStep) {
            $stepFactory = app()->make(StepFactory::class);
            $currentStep = $stepFactory->buildFromModel($conversation->currentStep, false, true); // No flow, but WITH component to load buttons
        }

        $client = null;
        if ($with_relations && $conversation->client_id && $conversation->relationLoaded('client') && $conversation->client) {
            $client = $this->clientFactory->buildFromModel($conversation->client);
        }

        // Extract WhatsApp metadata from JSON
        $jsonData = $conversation->json ? json_decode($conversation->json, true) : [];
        $whatsappContactName = $conversation->whatsapp_contact_name ?? $jsonData['whatsapp_contact_name'] ?? null;
        $whatsappProfileName = $conversation->whatsapp_profile_name ?? $jsonData['whatsapp_profile_name'] ?? null;
        $whatsappMetadata = $conversation->whatsapp_metadata ?? $jsonData['whatsapp_metadata'] ?? null;

        return new Conversation(
            id: $conversation->id ?? null,
            organization_id: $conversation->organization_id ?? null,
            user_id: $conversation->user_id ?? null,
            client_id: $conversation->client_id ?? null,
            flow_id: $conversation->flow_id ?? null,
            phone_number_id: $conversation->phone_number_id ?? null,
            current_step_id: $conversation->current_step_id ?? null,
            json: $conversation->json ?? null,
            is_finished: $conversation->is_finished ?? false,
            raw_data: $conversation->raw_data ?? null,
            created_at: $conversation->created_at ?? null,
            updated_at: $conversation->updated_at ?? null,
            user: ($with_relations && $conversation->user_id) ? $this->userFactory->buildFromModel($conversation->user ?? null) : null,
            flow: ($with_relations && $conversation->flow_id) ? $this->flowFactory->buildFromModel($conversation->flow ?? null, false) : null,
            phone_number: $phoneNumber,
            current_step: $currentStep,
            interactions: $interactions,
            client: $client,
            whatsapp_contact_name: $whatsappContactName,
            whatsapp_profile_name: $whatsappProfileName,
            whatsapp_metadata: $whatsappMetadata
        );
    }

    /**
     * @param Collection|null $conversations
     * @param bool $with_relations
     * @return Conversation[]|null
     */
    public function buildFromModels(?Collection $conversations, bool $with_relations = false): ?array
    {
        if (!$conversations) {
            return null;
        }

        $domains = [];

        /** @var ConversationModel $conversation */
        foreach ($conversations as $conversation) {
            $domains[] = $this->buildFromModel($conversation, $with_relations);
        }

        return $domains;
    }

    /**
     * Build new Conversation for client and phone number
     */
    public function buildForClientAndPhoneNumber(
        $client,
        $phoneNumber,
        $flow,
        array $whatsappData = []
    ): Conversation {
        // Parse WhatsApp metadata
        $metadata = [
            'whatsapp_contact_name' => $whatsappData['contact_name'] ?? null,
            'whatsapp_profile_name' => $whatsappData['profile_name'] ?? null,
            'whatsapp_metadata' => $whatsappData['metadata'] ?? [],
        ];

        return new Conversation(
            id: null,
            organization_id: $phoneNumber->organization_id,
            user_id: null,
            client_id: $client->id,
            flow_id: $flow->id,
            phone_number_id: $phoneNumber->id,
            current_step_id: null, // will be set when flow starts
            json: json_encode($metadata),
            is_finished: false,
            raw_data: null,
            created_at: null,
            updated_at: null,
            user: null,
            flow: $flow,
            phone_number: $phoneNumber,
            current_step: null, // will be set when flow starts
            interactions: [],
            client: null,
            whatsapp_contact_name: $metadata['whatsapp_contact_name'],
            whatsapp_profile_name: $metadata['whatsapp_profile_name'],
            whatsapp_metadata: $metadata['whatsapp_metadata']
        );
    }

    /**
     * Build Conversation from webhook message data
     */
    public function buildFromWebhookData(
        array $messageData,
        $client,
        $phoneNumber,
        $flow
    ): Conversation {
        $whatsappData = [
            'contact_name' => $messageData['contact_name'] ?? null,
            'profile_name' => $messageData['profile_name'] ?? null,
            'metadata' => [
                'from' => $messageData['from'] ?? null,
                'timestamp' => $messageData['timestamp'] ?? null,
                'message_id' => $messageData['message_id'] ?? null,
            ]
        ];

        $conversation = $this->buildForClientAndPhoneNumber(
            $client,
            $phoneNumber,
            $flow,
            $whatsappData
        );

        // Set raw_data from messageData if available
        if (isset($messageData['raw_data'])) {
            $conversation->raw_data = is_string($messageData['raw_data'])
                ? json_decode($messageData['raw_data'], true)
                : $messageData['raw_data'];
        }

        return $conversation;
    }

    /**
     * Build Conversation from MessageData domain
     */
    public function buildFromMessageData(
        $messageData,
        $client,
        $phoneNumber,
        $flow
    ): Conversation {
        $whatsappData = [
            'contact_name' => $messageData->contact_name,
            'profile_name' => $messageData->profile_name,
            'metadata' => [
                'from' => $messageData->from,
                'timestamp' => $messageData->timestamp,
                'message_id' => $messageData->message_id,
            ]
        ];

        $conversation = $this->buildForClientAndPhoneNumber(
            $client,
            $phoneNumber,
            $flow,
            $whatsappData
        );

        // Set raw_data from MessageData
        if ($messageData->raw_data) {
            $conversation->raw_data = is_string($messageData->raw_data)
                ? json_decode($messageData->raw_data, true)
                : $messageData->raw_data;
        }

        return $conversation;
    }
}
