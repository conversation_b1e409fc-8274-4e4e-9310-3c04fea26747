<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Interaction;
use App\Factories\Inventory\ClientFactory;
use App\Factories\UserFactory;
use App\Http\Requests\Interaction\StoreRequest;
use App\Http\Requests\Interaction\UpdateRequest;
use App\Models\Interaction as InteractionModel;
use App\Enums\ChatBot\InteractionState;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class InteractionFactory
{
    public UserFactory $userFactory;
    public ClientFactory $clientFactory;
    public FlowFactory $flowFactory;
    public StepFactory $stepFactory;

    public function __construct(
        UserFactory $userFactory,
        ClientFactory $clientFactory,
        FlowFactory $flowFactory,
        StepFactory $stepFactory
    ) {
        $this->userFactory = $userFactory;
        $this->clientFactory = $clientFactory;
        $this->flowFactory = $flowFactory;
        $this->stepFactory = $stepFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): Interaction
    {
        return new Interaction(
            id: null,
            organization_id: $request->organization_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            flow_id: $request->flow_id ?? null,
            step_id: $request->step_id ?? null,
            conversation_id: $request->conversation_id ?? null,
            message: $request->message ?? null,
            answer: $request->answer ?? null,
            result: $request->result ?? null,
            json: $request->json ?? null,
            is_button_click: $request->is_button_click ?? false,
            button_click_id: $request->button_click_id ?? null,
            is_raw_message: $request->is_raw_message ?? false,
            raw_data: $request->raw_data ?? null,
            state: InteractionState::from($request->state ?? 'pending'),
            sent_at: $request->sent_at ? Carbon::parse($request->sent_at) : null,
            responded_at: $request->responded_at ? Carbon::parse($request->responded_at) : null,
            processed_at: $request->processed_at ? Carbon::parse($request->processed_at) : null,
            requires_response: $request->requires_response ?? true,
            expected_response_type: $request->expected_response_type ?? 'any',
            timeout_seconds: $request->timeout_seconds ?? 300,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): Interaction
    {
        return new Interaction(
            id: null,
            organization_id: $request->organization_id ?? null,
            user_id: $request->user_id ?? null,
            client_id: $request->client_id ?? null,
            flow_id: $request->flow_id ?? null,
            step_id: $request->step_id ?? null,
            conversation_id: $request->conversation_id ?? null,
            message: $request->message ?? null,
            answer: $request->answer ?? null,
            result: $request->result ?? null,
            json: $request->json ?? null,
            is_button_click: $request->is_button_click ?? false,
            button_click_id: $request->button_click_id ?? null,
            is_raw_message: $request->is_raw_message ?? false,
            raw_data: $request->raw_data ?? null,
            state: InteractionState::from($request->state ?? 'pending'),
            sent_at: $request->sent_at ? Carbon::parse($request->sent_at) : null,
            responded_at: $request->responded_at ? Carbon::parse($request->responded_at) : null,
            processed_at: $request->processed_at ? Carbon::parse($request->processed_at) : null,
            requires_response: $request->requires_response ?? true,
            expected_response_type: $request->expected_response_type ?? 'any',
            timeout_seconds: $request->timeout_seconds ?? 300
        );
    }

    public function buildFromModel(?InteractionModel $interaction, bool $with_relations = true): ?Interaction
    {
        if (!$interaction) {
            return null;
        }

        // Extract WhatsApp data from JSON
        $jsonData = $interaction->json ? json_decode($interaction->json, true) : [];
        $whatsappMessageId = $interaction->whatsapp_message_id ?? $jsonData['whatsapp_message_id'] ?? null;
        $whatsappMessageType = $interaction->whatsapp_message_type ?? $jsonData['whatsapp_message_type'] ?? null;
        $whatsappRawData = $interaction->whatsapp_raw_data ?? $jsonData['whatsapp_raw_data'] ?? null;

        return new Interaction(
            id: $interaction->id ?? null,
            organization_id: $interaction->organization_id ?? null,
            user_id: $interaction->user_id ?? null,
            client_id: $interaction->client_id ?? null,
            flow_id: $interaction->flow_id ?? null,
            step_id: $interaction->step_id ?? null,
            conversation_id: $interaction->conversation_id ?? null,
            message: $interaction->message ?? null,
            answer: $interaction->answer ?? null,
            result: $interaction->result ?? null,
            json: $interaction->json ?? null,
            is_button_click: $interaction->is_button_click ?? false,
            button_click_id: $interaction->button_click_id ?? null,
            is_raw_message: $interaction->is_raw_message ?? false,
            raw_data: $interaction->raw_data ?? null,
            state: InteractionState::from($interaction->state ?? 'pending'),
            sent_at: $interaction->sent_at ?? null,
            responded_at: $interaction->responded_at ?? null,
            processed_at: $interaction->processed_at ?? null,
            requires_response: $interaction->requires_response ?? true,
            expected_response_type: $interaction->expected_response_type ?? 'any',
            timeout_seconds: $interaction->timeout_seconds ?? 300,
            created_at: $interaction->created_at ?? null,
            updated_at: $interaction->updated_at ?? null,
            user: ($with_relations && $interaction->user_id) ? $this->userFactory->buildFromModel($interaction->user ?? null) : null,
            flow: ($with_relations && $interaction->flow_id) ? $this->flowFactory->buildFromModel($interaction->flow ?? null, false) : null,
            step: ($with_relations && $interaction->step_id) ? $this->stepFactory->buildFromModel($interaction->step ?? null, false, false) : null,
            conversation: ($with_relations && $interaction->conversation_id) ? app()->make(ConversationFactory::class)->buildFromModel($interaction->conversation ?? null, false) : null,
            whatsapp_message_id: $whatsappMessageId,
            whatsapp_message_type: $whatsappMessageType,
            whatsapp_raw_data: $whatsappRawData
        );
    }

    /**
     * @param Collection|null $interactions
     * @param bool $with_relations
     * @return Interaction[]|null
     */
    public function buildFromModels(?Collection $interactions, bool $with_relations = false): ?array
    {
        if (!$interactions) {
            return null;
        }

        $domains = [];

        /** @var InteractionModel $interaction */
        foreach ($interactions as $interaction) {
            $domains[] = $this->buildFromModel($interaction, $with_relations);
        }

        return $domains;
    }

    /**
     * Build Interaction from webhook message data
     */
    public function buildFromWebhookData(
        $messageData,
        $conversation
    ): Interaction {
        $messageType = $messageData->type ?? 'text';
        $messageContent = $messageData->content;

        // Use raw_message data for WhatsApp interaction detection
        $rawData = $messageData->raw_message ?? $messageData->toArray();

        // Store WhatsApp data in the JSON field
        $whatsappData = [
            'whatsapp_message_id' => $messageData->message_id ?? null,
            'whatsapp_message_type' => $messageType,
            'whatsapp_raw_data' => $rawData,
        ];

        return new Interaction(
            id: null,
            organization_id: $conversation->organization_id,
            user_id: $conversation->user_id,
            client_id: $conversation->client_id,
            flow_id: $conversation->flow_id,
            step_id: $conversation->current_step_id,
            conversation_id: $conversation->id,
            message: $messageContent,
            answer: null, // will be set when response is generated
            result: null, // will be set after processing
            json: json_encode($whatsappData),
            is_button_click: $messageData->is_button_click ?? false,
            button_click_id: $messageData->button_click_id ?? null,
            is_raw_message: $messageData->is_raw_message ?? false,
            raw_data: $messageData->raw_data ?? null,
            state: InteractionState::PENDING,
            sent_at: null,
            responded_at: null,
            processed_at: null,
            requires_response: true,
            expected_response_type: $messageData->is_button_click ? 'button' : 'any',
            timeout_seconds: 300, // 5 minutes default
            created_at: null,
            updated_at: null,
            conversation: $conversation,
            whatsapp_message_id: $messageData->message_id ?? null,
            whatsapp_message_type: $messageType,
            whatsapp_raw_data: $rawData
        );
    }

    /**
     * Build Interaction from legacy array format (for backward compatibility)
     */
    public function buildFromWebhookDataArray(
        array $messageData,
        $conversation
    ): Interaction {
        $messageType = $messageData['type'] ?? 'text';
        $messageContent = $this->extractMessageContent($messageData);

        // Use raw_message data for WhatsApp interaction detection
        $rawData = $messageData['raw_message'] ?? $messageData;

        // Store WhatsApp data in the JSON field
        $whatsappData = [
            'whatsapp_message_id' => $messageData['message_id'] ?? null,
            'whatsapp_message_type' => $messageType,
            'whatsapp_raw_data' => $rawData,
        ];

        return new Interaction(
            id: null,
            organization_id: $conversation->organization_id,
            user_id: $conversation->user_id,
            client_id: $conversation->client_id,
            flow_id: $conversation->flow_id,
            step_id: $conversation->current_step_id,
            conversation_id: $conversation->id,
            message: $messageContent,
            answer: null, // will be set when response is generated
            result: null, // will be set after processing
            json: json_encode($whatsappData),
            is_button_click: $messageData['is_button_click'] ?? false,
            button_click_id: $messageData['button_click_id'] ?? null,
            is_raw_message: $messageData['is_raw_message'] ?? false,
            raw_data: $messageData['raw_data'] ?? null,
            state: InteractionState::PENDING,
            sent_at: null,
            responded_at: null,
            processed_at: null,
            requires_response: true,
            expected_response_type: ($messageData['is_button_click'] ?? false) ? 'button' : 'any',
            timeout_seconds: 300, // 5 minutes default
            created_at: null,
            updated_at: null,
            conversation: $conversation,
            whatsapp_message_id: $messageData['message_id'] ?? null,
            whatsapp_message_type: $messageType,
            whatsapp_raw_data: $rawData
        );
    }

    /**
     * Extract message content based on message type
     */
    private function extractMessageContent(array $messageData): ?string
    {
        // Use raw_message if available, otherwise use messageData directly
        $rawMessage = $messageData['raw_message'] ?? $messageData;
        $type = $messageData['type'] ?? 'text';

        switch ($type) {
            case 'text':
                return $rawMessage['text']['body'] ?? null;

            case 'interactive':
                $interactive = $rawMessage['interactive'] ?? [];
                $interactiveType = $interactive['type'] ?? null;

                if ($interactiveType === 'button_reply') {
                    return $interactive['button_reply']['title'] ?? null;
                } elseif ($interactiveType === 'list_reply') {
                    return $interactive['list_reply']['title'] ?? null;
                }
                break;

            case 'image':
            case 'document':
            case 'audio':
            case 'video':
                return "Media message: {$type}";

            default:
                return "Unsupported message type: {$type}";
        }

        return null;
    }
}
