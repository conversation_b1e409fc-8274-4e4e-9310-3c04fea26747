<?php

namespace App\Domains\ChatBot;

use App\Domains\User;
use Carbon\Carbon;
use App\Domains\ChatBot\Conversation;
use App\Enums\ChatBot\InteractionState;

class Interaction
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $flow_id;
    public ?int $step_id;
    public ?int $conversation_id;
    public ?string $message;
    public ?string $answer;
    public ?string $result;
    public ?string $json;
    public ?bool $is_button_click;
    public ?int $button_click_id;
    public ?bool $is_raw_message;
    public ?string $raw_data;
    public InteractionState $state;
    public ?Carbon $sent_at;
    public ?Carbon $responded_at;
    public ?Carbon $processed_at;
    public bool $requires_response;
    public string $expected_response_type;
    public ?int $timeout_seconds;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Flow $flow;
    public ?Step $step;
    public ?Conversation $conversation;

    // WhatsApp specific properties
    public ?string $whatsapp_message_id;
    public ?string $whatsapp_message_type;
    public ?array $whatsapp_raw_data;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        ?int $client_id = null,
        ?int $flow_id = null,
        ?int $step_id = null,
        ?int $conversation_id = null,
        ?string $message = null,
        ?string $answer = null,
        ?string $result = null,
        ?string $json = null,
        ?bool $is_button_click = false,
        ?int $button_click_id = null,
        ?bool $is_raw_message = false,
        ?string $raw_data = null,
        InteractionState $state = InteractionState::PENDING,
        ?Carbon $sent_at = null,
        ?Carbon $responded_at = null,
        ?Carbon $processed_at = null,
        bool $requires_response = true,
        string $expected_response_type = 'any',
        ?int $timeout_seconds = 300,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Flow $flow = null,
        ?Step $step = null,
        ?Conversation $conversation = null,
        ?string $whatsapp_message_id = null,
        ?string $whatsapp_message_type = null,
        ?array $whatsapp_raw_data = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->flow_id = $flow_id;
        $this->step_id = $step_id;
        $this->conversation_id = $conversation_id;
        $this->message = $message;
        $this->answer = $answer;
        $this->result = $result;
        $this->json = $json;
        $this->is_button_click = $is_button_click;
        $this->button_click_id = $button_click_id;
        $this->is_raw_message = $is_raw_message;
        $this->raw_data = $raw_data;
        $this->state = $state;
        $this->sent_at = $sent_at;
        $this->responded_at = $responded_at;
        $this->processed_at = $processed_at;
        $this->requires_response = $requires_response;
        $this->expected_response_type = $expected_response_type;
        $this->timeout_seconds = $timeout_seconds;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->flow = $flow;
        $this->step = $step;
        $this->conversation = $conversation;
        $this->whatsapp_message_id = $whatsapp_message_id;
        $this->whatsapp_message_type = $whatsapp_message_type;
        $this->whatsapp_raw_data = $whatsapp_raw_data;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
            "is_button_click" => $this->is_button_click,
            "button_click_id" => $this->button_click_id,
            "is_raw_message" => $this->is_raw_message,
            "raw_data" => $this->raw_data,
            "state" => $this->state->value,
            "sent_at" => $this->sent_at?->format("Y-m-d H:i:s"),
            "responded_at" => $this->responded_at?->format("Y-m-d H:i:s"),
            "processed_at" => $this->processed_at?->format("Y-m-d H:i:s"),
            "requires_response" => $this->requires_response,
            "expected_response_type" => $this->expected_response_type,
            "timeout_seconds" => $this->timeout_seconds,
            "whatsapp_message_id" => $this->whatsapp_message_id,
            "whatsapp_message_type" => $this->whatsapp_message_type,
            "whatsapp_raw_data" => $this->whatsapp_raw_data,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => $this->user?->toArray(),
            "flow" => $this->flow?->toArray(),
            "step" => $this->step?->toArray(),
            "conversation" => $this->conversation?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
            "is_button_click" => $this->is_button_click,
            "button_click_id" => $this->button_click_id,
            "is_raw_message" => $this->is_raw_message,
            "raw_data" => $this->raw_data,
            "state" => $this->state->value,
            "sent_at" => $this->sent_at,
            "responded_at" => $this->responded_at,
            "processed_at" => $this->processed_at,
            "requires_response" => $this->requires_response,
            "expected_response_type" => $this->expected_response_type,
            "timeout_seconds" => $this->timeout_seconds,
            "whatsapp_message_id" => $this->whatsapp_message_id,
            "whatsapp_message_type" => $this->whatsapp_message_type,
            "whatsapp_raw_data" => $this->whatsapp_raw_data,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "step_id" => $this->step_id,
            "conversation_id" => $this->conversation_id,
            "message" => $this->message,
            "answer" => $this->answer,
            "result" => $this->result,
            "json" => $this->json,
            "is_button_click" => $this->is_button_click,
            "button_click_id" => $this->button_click_id,
            "is_raw_message" => $this->is_raw_message,
            "raw_data" => $this->raw_data,
            "state" => $this->state->value,
            "sent_at" => $this->sent_at,
            "responded_at" => $this->responded_at,
            "processed_at" => $this->processed_at,
            "requires_response" => $this->requires_response,
            "expected_response_type" => $this->expected_response_type,
            "timeout_seconds" => $this->timeout_seconds,
        ];
    }

    // State management methods
    public function markAsSent(?Carbon $sentAt = null): void
    {
        $this->state = InteractionState::SENT;
        $this->sent_at = $sentAt ?? now();
    }

    public function markAsResponded(?Carbon $respondedAt = null): void
    {
        $this->state = InteractionState::RESPONDED;
        $this->responded_at = $respondedAt ?? now();
    }

    public function markAsProcessed(?Carbon $processedAt = null): void
    {
        $this->state = InteractionState::PROCESSED;
        $this->processed_at = $processedAt ?? now();
    }

    public function markAsCompleted(?Carbon $completedAt = null): void
    {
        $this->state = InteractionState::COMPLETED;
        $this->processed_at = $completedAt ?? now();
    }

    public function markAsExpired(?Carbon $expiredAt = null): void
    {
        $this->state = InteractionState::EXPIRED;
        $this->processed_at = $expiredAt ?? now();
    }

    public function isWaitingForResponse(): bool
    {
        return $this->state === InteractionState::SENT && $this->requires_response;
    }

    public function isFinished(): bool
    {
        return in_array($this->state, [
            InteractionState::PROCESSED,
            InteractionState::COMPLETED,
            InteractionState::EXPIRED
        ]);
    }

    public function hasTimedOut(): bool
    {
        if (!$this->timeout_seconds || !$this->sent_at) {
            return false;
        }

        return $this->sent_at->addSeconds($this->timeout_seconds)->isPast();
    }

    /**
     * Check if this is a button interaction
     */
    public function isButtonInteraction(): bool
    {
        // Use direct properties if available, fallback to JSON parsing
        if ($this->whatsapp_message_type && $this->whatsapp_raw_data) {
            return $this->whatsapp_message_type === 'interactive' &&
                   isset($this->whatsapp_raw_data['interactive']['type']) &&
                   $this->whatsapp_raw_data['interactive']['type'] === 'button_reply';
        }

        // Fallback to JSON parsing for backward compatibility
        $jsonData = $this->json ? json_decode($this->json, true) : [];
        $whatsappMessageType = $jsonData['whatsapp_message_type'] ?? null;
        $whatsappRawData = $jsonData['whatsapp_raw_data'] ?? [];

        return $whatsappMessageType === 'interactive' &&
               isset($whatsappRawData['interactive']['type']) &&
               $whatsappRawData['interactive']['type'] === 'button_reply';
    }

    /**
     * Check if this is a list interaction
     */
    public function isListInteraction(): bool
    {
        // Use direct properties if available, fallback to JSON parsing
        if ($this->whatsapp_message_type && $this->whatsapp_raw_data) {
            return $this->whatsapp_message_type === 'interactive' &&
                   isset($this->whatsapp_raw_data['interactive']['type']) &&
                   $this->whatsapp_raw_data['interactive']['type'] === 'list_reply';
        }

        // Fallback to JSON parsing for backward compatibility
        $jsonData = $this->json ? json_decode($this->json, true) : [];
        $whatsappMessageType = $jsonData['whatsapp_message_type'] ?? null;
        $whatsappRawData = $jsonData['whatsapp_raw_data'] ?? [];

        return $whatsappMessageType === 'interactive' &&
               isset($whatsappRawData['interactive']['type']) &&
               $whatsappRawData['interactive']['type'] === 'list_reply';
    }

    /**
     * Check if this is a text message
     */
    public function isTextMessage(): bool
    {
        // Use direct property if available, fallback to JSON parsing
        if ($this->whatsapp_message_type) {
            return $this->whatsapp_message_type === 'text';
        }

        // Fallback to JSON parsing for backward compatibility
        $jsonData = $this->json ? json_decode($this->json, true) : [];
        $whatsappMessageType = $jsonData['whatsapp_message_type'] ?? null;

        return $whatsappMessageType === 'text';
    }

    /**
     * Get button ID from interaction
     */
    public function getButtonId(): ?string
    {
        if (!$this->isButtonInteraction()) {
            return null;
        }

        // Use direct property if available, fallback to JSON parsing
        if ($this->whatsapp_raw_data) {
            return $this->whatsapp_raw_data['interactive']['button_reply']['id'] ?? null;
        }

        // Fallback to JSON parsing for backward compatibility
        $jsonData = $this->json ? json_decode($this->json, true) : [];
        $whatsappRawData = $jsonData['whatsapp_raw_data'] ?? [];

        return $whatsappRawData['interactive']['button_reply']['id'] ?? null;
    }

    /**
     * Get list selection ID from interaction
     */
    public function getListSelectionId(): ?string
    {
        if (!$this->isListInteraction()) {
            return null;
        }

        // Use direct property if available, fallback to JSON parsing
        if ($this->whatsapp_raw_data) {
            return $this->whatsapp_raw_data['interactive']['list_reply']['id'] ?? null;
        }

        // Fallback to JSON parsing for backward compatibility
        $jsonData = $this->json ? json_decode($this->json, true) : [];
        $whatsappRawData = $jsonData['whatsapp_raw_data'] ?? [];

        return $whatsappRawData['interactive']['list_reply']['id'] ?? null;
    }

    /**
     * Get text content from message
     */
    public function getTextContent(): ?string
    {
        if ($this->isTextMessage()) {
            // Use direct property if available, fallback to JSON parsing
            if ($this->whatsapp_raw_data) {
                return $this->whatsapp_raw_data['text']['body'] ?? null;
            }

            // Fallback to JSON parsing for backward compatibility
            $jsonData = $this->json ? json_decode($this->json, true) : [];
            $whatsappRawData = $jsonData['whatsapp_raw_data'] ?? [];

            return $whatsappRawData['text']['body'] ?? null;
        }

        return $this->message;
    }

    /**
     * Get input text (alias for getTextContent for compatibility)
     */
    public function getInputText(): ?string
    {
        return $this->getTextContent();
    }

    /**
     * Magic getter for input_text property
     */
    public function __get($property)
    {
        if ($property === 'input_text') {
            return $this->getInputText();
        }

        if ($property === 'message_text') {
            return $this->message;
        }

        if ($property === 'whatsapp_message_type') {
            $jsonData = $this->json ? json_decode($this->json, true) : [];
            return $jsonData['whatsapp_message_type'] ?? null;
        }

        return null;
    }

}
