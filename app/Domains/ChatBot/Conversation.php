<?php

namespace App\Domains\ChatBot;

use App\Domains\Inventory\Client;
use App\Domains\User;
use Carbon\Carbon;
use App\Domains\ChatBot\Interaction;
use Illuminate\Support\Facades\Log;

class Conversation
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $flow_id;
    public ?int $phone_number_id;
    public ?int $current_step_id;
    public ?string $json;
    public ?bool $is_finished;
    public ?array $raw_data;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Flow $flow;
    public ?PhoneNumber $phone_number;
    public ?Step $current_step;
    public ?Client $client;

    /** @var Interaction[]|null $interactions  */
    public ?array $interactions;

    // WhatsApp specific properties
    public ?string $whatsapp_contact_name;
    public ?string $whatsapp_profile_name;
    public ?array $whatsapp_metadata;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        ?int $client_id = null,
        ?int $flow_id = null,
        ?int $phone_number_id = null,
        ?int $current_step_id = null,
        ?string $json = null,
        ?bool $is_finished = null,
        ?array $raw_data = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Flow $flow = null,
        ?PhoneNumber $phone_number = null,
        ?Step $current_step = null,
        ?array $interactions = null,
        ?Client $client = null,
        ?string $whatsapp_contact_name = null,
        ?string $whatsapp_profile_name = null,
        ?array $whatsapp_metadata = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->flow_id = $flow_id;
        $this->phone_number_id = $phone_number_id;
        $this->current_step_id = $current_step_id;
        $this->json = $json;
        $this->is_finished = $is_finished;
        $this->raw_data = $raw_data;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->flow = $flow;
        $this->phone_number = $phone_number;
        $this->current_step = $current_step;
        $this->interactions = $interactions;
        $this->client = $client;
        $this->whatsapp_contact_name = $whatsapp_contact_name;
        $this->whatsapp_profile_name = $whatsapp_profile_name;
        $this->whatsapp_metadata = $whatsapp_metadata;
    }

    public function toArray(): array
    {
        $interactionsArray = null;
        if ($this->interactions) {
            $interactionsArray = [];
            foreach ($this->interactions as $interaction) {
                $interactionsArray[] = $interaction->toArray();
            }
        }

        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
            "raw_data" => $this->raw_data,
            "whatsapp_contact_name" => $this->whatsapp_contact_name,
            "whatsapp_profile_name" => $this->whatsapp_profile_name,
            "whatsapp_metadata" => $this->whatsapp_metadata,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => $this->user?->toArray(),
            "flow" => $this->flow?->toArray(),
            "phone_number" => $this->phone_number?->toArray(),
            "current_step" => $this->current_step?->toArray(),
            "client" => $this->client?->toArray(),
            "interactions" => $interactionsArray,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
            "raw_data" => $this->raw_data,
            "whatsapp_contact_name" => $this->whatsapp_contact_name,
            "whatsapp_profile_name" => $this->whatsapp_profile_name,
            "whatsapp_metadata" => $this->whatsapp_metadata,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
            "raw_data" => $this->raw_data,
        ];
    }

    /**
     * Check if conversation is at the beginning of flow
     */
    public function isAtStart(): bool
    {
        return $this->current_step_id === null ||
               ($this->current_step && $this->current_step->is_initial_step);
    }

    /**
     * Check if conversation has finished the flow
     */
    public function isCompleted(): bool
    {
        return $this->is_finished ||
               ($this->current_step && $this->current_step->is_ending_step);
    }

    /**
     * Get next step in the flow
     */
    public function getNextStep(): ?Step
    {
        if (!$this->current_step || !$this->current_step->next_step) {
            return null;
        }

        // TODO: Load next step from repository
        return null;
    }

    /**
     * Move conversation to next step
     */
    public function moveToNextStep(?Step $nextStep = null): void
    {
        if ($nextStep) {
            $this->current_step_id = $nextStep->id;
            $this->current_step = $nextStep;
        } else {
            $nextStep = $this->getNextStep();
            if ($nextStep) {
                $this->current_step_id = $nextStep->id;
                $this->current_step = $nextStep;
            }
        }

        // Mark as finished if we reached an ending step
        if ($this->current_step && $this->current_step->is_ending_step) {
            $this->is_finished = true;
        }
    }

    /**
     * Reset conversation to start of flow
     */
    public function resetToStart(): void
    {
        $this->current_step_id = null;
        $this->current_step = null;
        $this->is_finished = false;
        $this->whatsapp_metadata = [];
    }

    /**
     * Mark conversation as closed due to inactivity
     *
     * @param Carbon $lastActivityTime
     * @param int $inactivityMinutes
     * @return void
     */
    public function markAsClosedByInactivity(Carbon $lastActivityTime, int $inactivityMinutes): void
    {
        $this->is_finished = true;
        $this->updated_at = Carbon::now();

        $conversationData = $this->json ? json_decode($this->json, true) : [];
        $conversationData['closure_info'] = [
            'closed_at' => Carbon::now()->toISOString(),
            'reason' => 'inactivity_timeout',
            'last_activity_at' => $lastActivityTime->toISOString(),
            'inactivity_minutes' => $inactivityMinutes,
            'closed_by' => 'system_cron'
        ];
        $this->json = json_encode($conversationData);
    }

    /**
     * Check if conversation is inactive based on last activity and timeout
     *
     * @param Carbon $lastActivityTime
     * @param int $inactivityMinutes
     * @return bool
     */
    public function isConversationInactive(Carbon $lastActivityTime, int $inactivityMinutes): bool
    {
        $cutoffTime = Carbon::now()->subMinutes($inactivityMinutes);
        return $lastActivityTime->isBefore($cutoffTime);
    }

    public function finish(): void
    {
        $this->is_finished = true;
        $this->current_step_id = null;
        $this->current_step = null;
    }

}
