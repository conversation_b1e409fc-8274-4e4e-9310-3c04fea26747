<?php

namespace App\Domains\ChatBot;

use App\Enums\MessageStatus;
use Carbon\Carbon;

class MessageDeliveryAttempt
{
    public ?int $id;
    public ?int $message_id;
    public ?int $attempt_number;
    public ?MessageStatus $status;
    public ?string $error_message;
    public ?array $whatsapp_response_json;
    public ?Carbon $attempted_at;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?Message $message;

    public function __construct(
        ?int $id = null,
        ?int $message_id = null,
        ?int $attempt_number = null,
        ?MessageStatus $status = null,
        ?string $error_message = null,
        ?array $whatsapp_response_json = null,
        ?Carbon $attempted_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Message $message = null
    ) {
        $this->id = $id;
        $this->message_id = $message_id;
        $this->attempt_number = $attempt_number;
        $this->status = $status;
        $this->error_message = $error_message;
        $this->whatsapp_response_json = $whatsapp_response_json;
        $this->attempted_at = $attempted_at ?? now();
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->message = $message;
    }

    public function toStoreArray(): array
    {
        return [
            'message_id' => $this->message_id,
            'attempt_number' => $this->attempt_number,
            'status' => $this->status?->value,
            'error_message' => $this->error_message,
            'whatsapp_response_json' => $this->whatsapp_response_json,
            'attempted_at' => $this->attempted_at,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'message_id' => $this->message_id,
            'attempt_number' => $this->attempt_number,
            'status' => $this->status?->value,
            'status_label' => $this->status?->name,
            'error_message' => $this->error_message,
            'whatsapp_response_json' => $this->whatsapp_response_json,
            'attempted_at' => $this->attempted_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'message' => $this->message?->toArray(),
            'was_successful' => $this->wasSuccessful(),
            'duration_since_attempt_minutes' => $this->getDurationSinceAttempt(),
        ];
    }

    /**
     * Create a new delivery attempt
     */
    public static function create(
        int $message_id,
        int $attempt_number,
        MessageStatus $status,
        ?string $error_message = null,
        ?array $whatsapp_response = null
    ): self {
        return new self(
            message_id: $message_id,
            attempt_number: $attempt_number,
            status: $status,
            error_message: $error_message,
            whatsapp_response_json: $whatsapp_response,
            attempted_at: now()
        );
    }

    /**
     * Check if this attempt was successful
     */
    public function wasSuccessful(): bool
    {
        return $this->status === MessageStatus::is_sent;
    }

    /**
     * Check if this attempt failed
     */
    public function failed(): bool
    {
        return $this->status === MessageStatus::is_failed;
    }

    /**
     * Get the duration since this attempt in minutes
     */
    public function getDurationSinceAttempt(): int
    {
        if (!$this->attempted_at) {
            return 0;
        }
        return now()->diffInMinutes($this->attempted_at);
    }

    /**
     * Check if this attempt is recent (within last hour)
     */
    public function isRecent(): bool
    {
        return $this->getDurationSinceAttempt() <= 60;
    }

    /**
     * Get error summary for display
     */
    public function getErrorSummary(): ?string
    {
        if (!$this->error_message) {
            return null;
        }

        // Truncate long error messages
        if (strlen($this->error_message) > 100) {
            return substr($this->error_message, 0, 97) . '...';
        }

        return $this->error_message;
    }
}
